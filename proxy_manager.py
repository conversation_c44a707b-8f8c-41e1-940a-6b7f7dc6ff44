import random
import time
from threading import Lock
from typing import List, Optional, Dict
from logger import logger

class ProxyManager:
    def __init__(self, proxy_file: str = "proxies.txt"):
        self.proxy_file = proxy_file
        self.proxies: List[Dict] = []
        self.current_index = 0
        self.lock = Lock()
        self.failed_proxies = set()
        self.proxy_stats = {}
        
        self.load_proxies()
    
    def load_proxies(self):
        """Load proxies from file"""
        try:
            with open(self.proxy_file, 'r', encoding='utf-8') as f:
                lines = f.read().strip().split('\n')
            
            for line in lines:
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                
                proxy_dict = self.parse_proxy(line)
                if proxy_dict:
                    self.proxies.append(proxy_dict)
                    self.proxy_stats[line] = {
                        'requests': 0,
                        'failures': 0,
                        'last_used': 0
                    }
            
            logger.info(f"Loaded {len(self.proxies)} proxies")
            
        except FileNotFoundError:
            logger.warning(f"Proxy file {self.proxy_file} not found. Running without proxies.")
        except Exception as e:
            logger.error(f"Error loading proxies: {e}")
    
    def parse_proxy(self, proxy_string: str) -> Optional[Dict]:
        """Parse proxy string into dictionary"""
        try:
            # Support formats: ip:port, ip:port:user:pass, user:pass@ip:port
            if '@' in proxy_string:
                # Format: user:pass@ip:port
                auth, address = proxy_string.split('@')
                username, password = auth.split(':')
                ip, port = address.split(':')
                
                return {
                    'http': f'http://{username}:{password}@{ip}:{port}',
                    'https': f'http://{username}:{password}@{ip}:{port}',
                    'raw': proxy_string
                }
            else:
                parts = proxy_string.split(':')
                if len(parts) == 2:
                    # Format: ip:port
                    ip, port = parts
                    return {
                        'http': f'http://{ip}:{port}',
                        'https': f'http://{ip}:{port}',
                        'raw': proxy_string
                    }
                elif len(parts) == 4:
                    # Format: ip:port:user:pass
                    ip, port, username, password = parts
                    return {
                        'http': f'http://{username}:{password}@{ip}:{port}',
                        'https': f'http://{username}:{password}@{ip}:{port}',
                        'raw': proxy_string
                    }
            
            return None
            
        except Exception as e:
            logger.error(f"Error parsing proxy {proxy_string}: {e}")
            return None
    
    def get_proxy(self) -> Optional[Dict]:
        """Get next proxy in rotation"""
        if not self.proxies:
            return None
        
        with self.lock:
            # Filter out failed proxies
            available_proxies = [p for p in self.proxies if p['raw'] not in self.failed_proxies]
            
            if not available_proxies:
                logger.warning("All proxies failed, resetting failed list")
                self.failed_proxies.clear()
                available_proxies = self.proxies
            
            # Get next proxy
            proxy = available_proxies[self.current_index % len(available_proxies)]
            self.current_index += 1
            
            # Update stats
            self.proxy_stats[proxy['raw']]['requests'] += 1
            self.proxy_stats[proxy['raw']]['last_used'] = time.time()
            
            return proxy
    
    def mark_proxy_failed(self, proxy: Dict):
        """Mark a proxy as failed"""
        if proxy:
            with self.lock:
                self.failed_proxies.add(proxy['raw'])
                self.proxy_stats[proxy['raw']]['failures'] += 1
                logger.debug(f"Marked proxy as failed: {proxy['raw']}")
    
    def get_random_proxy(self) -> Optional[Dict]:
        """Get a random proxy instead of rotating"""
        if not self.proxies:
            return None
        
        available_proxies = [p for p in self.proxies if p['raw'] not in self.failed_proxies]
        
        if not available_proxies:
            self.failed_proxies.clear()
            available_proxies = self.proxies
        
        proxy = random.choice(available_proxies)
        
        with self.lock:
            self.proxy_stats[proxy['raw']]['requests'] += 1
            self.proxy_stats[proxy['raw']]['last_used'] = time.time()
        
        return proxy
    
    def get_stats(self) -> Dict:
        """Get proxy usage statistics"""
        total_proxies = len(self.proxies)
        failed_proxies = len(self.failed_proxies)
        active_proxies = total_proxies - failed_proxies
        
        return {
            'total_proxies': total_proxies,
            'active_proxies': active_proxies,
            'failed_proxies': failed_proxies,
            'proxy_stats': self.proxy_stats
        }
