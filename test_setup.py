#!/usr/bin/env python3
"""
Test script to verify the Binance Email Checker setup
"""

import sys
import os

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import requests
        print("✅ requests")
    except ImportError:
        print("❌ requests - Run: pip install requests")
        return False
    
    try:
        import colorama
        print("✅ colorama")
    except ImportError:
        print("❌ colorama - Run: pip install colorama")
        return False
    
    try:
        import tqdm
        print("✅ tqdm")
    except ImportError:
        print("❌ tqdm - Run: pip install tqdm")
        return False
    
    try:
        import fake_useragent
        print("✅ fake_useragent")
    except ImportError:
        print("❌ fake_useragent - Run: pip install fake-useragent")
        return False
    
    try:
        from dotenv import load_dotenv
        print("✅ python-dotenv")
    except ImportError:
        print("❌ python-dotenv - Run: pip install python-dotenv")
        return False
    
    return True

def test_local_modules():
    """Test if local modules can be imported"""
    print("\nTesting local modules...")
    
    modules = ['config', 'logger', 'utils', 'proxy_manager', 'email_checker', 'worker_manager']
    
    for module in modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module} - Error: {e}")
            return False
    
    return True

def test_files():
    """Test if required files exist"""
    print("\nTesting files...")
    
    required_files = ['emails.txt', 'proxies.txt', '.env']
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - File not found")
            return False
    
    return True

def test_basic_functionality():
    """Test basic functionality"""
    print("\nTesting basic functionality...")
    
    try:
        from utils import Utils
        utils = Utils()
        
        # Test email validation
        assert utils.validate_email("<EMAIL>") == True
        assert utils.validate_email("invalid-email") == False
        print("✅ Email validation")
        
        # Test UUID generation
        uuid1 = utils.generate_uuid()
        uuid2 = utils.generate_uuid()
        assert uuid1 != uuid2
        assert len(uuid1) > 0
        print("✅ UUID generation")
        
        # Test header generation
        headers = utils.generate_headers()
        assert 'User-Agent' in headers
        assert 'Content-Type' in headers
        print("✅ Header generation")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Binance Email Checker - Setup Test")
    print("=" * 40)
    
    tests = [
        test_imports,
        test_local_modules,
        test_files,
        test_basic_functionality
    ]
    
    all_passed = True
    
    for test in tests:
        if not test():
            all_passed = False
    
    print("\n" + "=" * 40)
    
    if all_passed:
        print("🎉 All tests passed! Setup is complete.")
        print("\nYou can now run the checker with:")
        print("python main.py")
    else:
        print("❌ Some tests failed. Please fix the issues above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
