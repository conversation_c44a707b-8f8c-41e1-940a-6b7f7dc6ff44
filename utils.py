import json
import csv
import os
import uuid
import random
import string
import time
from datetime import datetime
from typing import List, Dict, Any
from fake_useragent import UserAgent

class Utils:
    def __init__(self):
        self.ua = UserAgent()
    
    @staticmethod
    def load_emails(file_path: str) -> List[str]:
        """Load emails from file"""
        emails = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    email = line.strip()
                    if email and '@' in email:
                        emails.append(email)
            return emails
        except FileNotFoundError:
            raise FileNotFoundError(f"Email file {file_path} not found")
        except Exception as e:
            raise Exception(f"Error loading emails: {e}")
    
    @staticmethod
    def save_results(results: List[Dict], output_dir: str = "results"):
        """Save results to JSON and CSV files"""
        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save as JSON
        json_file = os.path.join(output_dir, f"results_{timestamp}.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        # Save as CSV
        csv_file = os.path.join(output_dir, f"results_{timestamp}.csv")
        if results:
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=results[0].keys())
                writer.writeheader()
                writer.writerows(results)
        
        return json_file, csv_file
    
    def generate_headers(self, email: str = None) -> Dict[str, str]:
        """Generate realistic headers for requests"""
        headers = {
            "User-Agent": self.ua.random,
            "Accept": "*/*",
            "Accept-Language": "en-US,en;q=0.5",
            "Content-Type": "application/json",
            "lang": "en",
            "clienttype": "web",
            "Sec-GPC": "1",
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "BNC-Time-Zone": "America/Los_Angeles",
            "csrftoken": "d41d8cd98f00b204e9800998ecf8427e",
            "X-UI-REQUEST-TRACE": self.generate_uuid(),
            "X-TRACE-ID": self.generate_uuid(),
            "BNC-UUID": self.generate_uuid(),
            "device-info": self.generate_device_info(),
            "FVIDEO-ID": self.generate_fvideo_id(),
            "FVIDEO-TOKEN": self.generate_fvideo_token(),
            "X-PASSTHROUGH-TOKEN": "",
            "BNC-Location": "",
            "Priority": "u=4"
        }
        
        return headers
    
    @staticmethod
    def generate_uuid() -> str:
        """Generate a random UUID"""
        return str(uuid.uuid4())
    
    @staticmethod
    def generate_fvideo_id() -> str:
        """Generate a random FVIDEO-ID"""
        return ''.join(random.choices(string.hexdigits.lower(), k=40))
    
    @staticmethod
    def generate_fvideo_token() -> str:
        """Generate a random FVIDEO-TOKEN"""
        chars = string.ascii_letters + string.digits + '+/='
        return ''.join(random.choices(chars, k=random.randint(80, 120)))
    
    @staticmethod
    def generate_device_info() -> str:
        """Generate device info (simplified version)"""
        # This is a simplified version - in production you might want more sophisticated fingerprinting
        return "eyJzY3JlZW5fcmVzb2x1dGlvbiI6IjM0NDAsMTQ0MCIsImF2YWlsYWJsZV9zY3JlZW5fcmVzb2x1dGlvbiI6IjM0NDAsMTM2NyIsInN5c3RlbV92ZXJzaW9uIjoiV2luZG93cyAxMCIsImJyYW5kX21vZGVsIjoidW5rbm93biIsInN5c3RlbV9sYW5nIjoiZW4tVVMiLCJ0aW1lem9uZSI6IkdNVC0wNzowMCIsInRpbWV6b25lT2Zmc2V0Ijo0MjAsInVzZXJfYWdlbnQiOiJNb3ppbGxhLzUuMCAoV2luZG93cyBOVCAxMC4wOyBXaW42NDsgeDY0OyBydjoxMzguMCkgR2Vja28vMjAxMDAxMDEgRmlyZWZveC8xMzguMCIsImxpc3RfcGx1Z2luIjoiUERGIFZpZXdlcixDaHJvbWUgUERGIFZpZXdlcixDaHJvbWl1bSBQREYgVmlld2VyLE1pY3Jvc29mdCBFZGdlIFBERiBWaWV3ZXIsV2ViS2l0IGJ1aWx0LWluIFBERiIsImNhbnZhc19jb2RlIjoiYmViYWMyZGYiLCJ3ZWJnbF92ZW5kb3IiOiJHb29nbGUgSW5jLiAoTlZJRElBKSIsIndlYmdsX3JlbmRlcmVyIjoiQU5HTEUgKE5WSURJQSwgTlZJRElBIEdlRm9yY2UgR1RYIDk4MCBEaXJlY3QzRDExIHZzXzVfMCBwc181XzApLCBvciBzaW1pbGFyIiwiYXVkaW8iOiIzNS43NDk5NzIwOTM4NTAzNzQiLCJwbGF0Zm9ybSI6IldpbjMyIiwid2ViX3RpbWV6b25lIjoiQW1lcmljYS9Mb3NfQW5nZWxlcyIsImRldmljZV9uYW1lIjoiRmlyZWZveCBWMTM4LjAgKFdpbmRvd3MpIiwiZmluZ2VycHJpbnQiOiJkOTg1ZmVjZDIxMGNmOGViMmFlNGVlY2ZjYmJjMjMyZSIsImRldmljZV9pZCI6IiIsInJlbGF0ZWRfZGV2aWNlX2lkcyI6IiJ9"
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """Basic email validation"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def rate_limit(requests_per_second: float):
        """Simple rate limiting"""
        if requests_per_second > 0:
            time.sleep(1.0 / requests_per_second)
    
    @staticmethod
    def format_duration(seconds: float) -> str:
        """Format duration in human readable format"""
        if seconds < 60:
            return f"{seconds:.1f}s"
        elif seconds < 3600:
            minutes = seconds / 60
            return f"{minutes:.1f}m"
        else:
            hours = seconds / 3600
            return f"{hours:.1f}h"
    
    @staticmethod
    def calculate_stats(results: List[Dict]) -> Dict[str, Any]:
        """Calculate statistics from results"""
        if not results:
            return {}
        
        total = len(results)
        registered = sum(1 for r in results if r.get('status') == 'registered')
        unregistered = sum(1 for r in results if r.get('status') == 'unregistered')
        errors = sum(1 for r in results if r.get('status') == 'error')
        
        return {
            'total_checked': total,
            'registered': registered,
            'unregistered': unregistered,
            'errors': errors,
            'registered_percentage': (registered / total * 100) if total > 0 else 0,
            'unregistered_percentage': (unregistered / total * 100) if total > 0 else 0,
            'error_percentage': (errors / total * 100) if total > 0 else 0
        }
