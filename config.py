import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # Threading settings
    MAX_WORKERS = int(os.getenv('MAX_WORKERS', '10'))
    
    # Request settings
    REQUEST_TIMEOUT = int(os.getenv('REQUEST_TIMEOUT', '30'))
    MAX_RETRIES = int(os.getenv('MAX_RETRIES', '3'))
    RETRY_DELAY = float(os.getenv('RETRY_DELAY', '1.0'))
    
    # Rate limiting
    REQUESTS_PER_SECOND = float(os.getenv('REQUESTS_PER_SECOND', '2.0'))
    
    # File paths
    EMAILS_FILE = os.getenv('EMAILS_FILE', 'emails.txt')
    PROXIES_FILE = os.getenv('PROXIES_FILE', 'proxies.txt')
    RESULTS_DIR = os.getenv('RESULTS_DIR', 'results')
    LOGS_DIR = os.getenv('LOGS_DIR', 'logs')
    
    # Binance API endpoints
    PRECHECK_URL = "https://accounts.binance.com/bapi/accounts/v1/public/account/security/request/precheck"
    CAPTCHA_URL = "https://accounts.binance.com/bapi/composite/v1/public/antibot/getCaptcha"
    VALIDATE_CAPTCHA_URL = "https://accounts.binance.com/bapi/composite/v1/public/antibot/validateCaptcha"
    CHECK_RESULT_URL = "https://accounts.binance.com/bapi/accounts/v1/public/account/security/check/result"
    BIZ_CHECK_URL = "https://accounts.binance.com/bapi/accounts/v1/public/account/security/bizCheck"
    
    # Headers template
    BASE_HEADERS = {
        "Accept": "*/*",
        "Accept-Language": "en-US,en;q=0.5",
        "Content-Type": "application/json",
        "lang": "en",
        "clienttype": "web",
        "Sec-GPC": "1",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin",
        "BNC-Time-Zone": "America/Los_Angeles",
        "csrftoken": "d41d8cd98f00b204e9800998ecf8427e"
    }
    
    # Device info template (base64 encoded)
    DEVICE_INFO = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
