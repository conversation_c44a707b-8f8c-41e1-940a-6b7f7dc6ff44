# Binance Email Checker - Usage Guide

## Quick Start

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the checker**:
   ```bash
   python main.py
   ```

## What This Tool Does

This tool checks if email addresses are registered on Binance by using the official Binance API endpoints. It simulates the login precheck process to determine if an email exists in their database.

### API Flow
1. **Precheck Request** - Sends email to Binance's precheck endpoint
2. **Response Analysis** - Analyzes the response to determine registration status
3. **Result Classification** - Classifies as registered, unregistered, or error

## Features

### ✅ Multi-threading
- Configurable number of worker threads
- Concurrent email checking for faster processing
- Thread-safe result collection

### ✅ Proxy Support
- Rotating proxy support to avoid IP blocking
- Multiple proxy formats supported
- Automatic proxy failure detection and rotation

### ✅ Comprehensive Logging
- Colored console output for easy reading
- Detailed file logs for debugging
- Separate results log for easy parsing

### ✅ Rate Limiting
- Configurable requests per second
- Random delays to avoid detection patterns
- Exponential backoff on failures

### ✅ Error Handling
- Automatic retry on failures
- Proxy rotation on errors
- Detailed error reporting

## File Structure

```
binance.com checker/
├── main.py              # Main application entry point
├── config.py            # Configuration settings
├── email_checker.py     # Core email checking logic
├── proxy_manager.py     # Proxy rotation management
├── worker_manager.py    # Multi-threading management
├── logger.py            # Logging system
├── utils.py             # Utility functions
├── requirements.txt     # Python dependencies
├── emails.txt           # Input email list
├── proxies.txt          # Proxy list
├── .env                 # Environment configuration
├── demo.py              # Demo script
├── simple_test.py       # Simple test script
├── test_setup.py        # Setup verification
├── run_checker.bat      # Windows batch file
├── run_checker.sh       # Linux/Mac shell script
└── README.md            # Documentation
```

## Configuration Options

### Environment Variables (.env)
```env
MAX_WORKERS=10              # Number of concurrent threads
REQUEST_TIMEOUT=30          # Request timeout in seconds
MAX_RETRIES=3              # Retry attempts on failure
RETRY_DELAY=1.0            # Base delay between retries
REQUESTS_PER_SECOND=2.0    # Rate limiting
```

### Command Line Options
```bash
python main.py -h                    # Show help
python main.py -e emails.txt         # Custom email file
python main.py -p proxies.txt        # Custom proxy file
python main.py -w 20                 # 20 worker threads
python main.py -r 1.5                # 1.5 requests per second
python main.py --create-samples      # Create sample files
```

## Input File Formats

### emails.txt
```
<EMAIL>
<EMAIL>
<EMAIL>
```

### proxies.txt
```
# Format 1: ip:port
*******:8080

# Format 2: ip:port:username:password
*******:3128:user:pass

# Format 3: username:password@ip:port
user:pass@**********:8080
```

## Output Formats

### Console Output
```
14:30:15 - INFO - Starting email check for 100 emails with 10 workers
14:30:16 - RESULT: REGISTERED: <EMAIL>
14:30:17 - RESULT: UNREGISTERED: <EMAIL>
```

### JSON Output (results/results_YYYYMMDD_HHMMSS.json)
```json
[
  {
    "email": "<EMAIL>",
    "status": "registered",
    "message": "Email found in Binance database",
    "timestamp": "2024-01-15 14:30:15",
    "duration": 1.23,
    "proxy_used": "*******:8080"
  }
]
```

### CSV Output (results/results_YYYYMMDD_HHMMSS.csv)
```csv
email,status,message,timestamp,duration,proxy_used
<EMAIL>,registered,Email found in Binance database,2024-01-15 14:30:15,1.23,*******:8080
```

## Status Types

- **registered**: Email is registered on Binance
- **unregistered**: Email is not registered on Binance
- **error**: Request failed (network, proxy, API error)

## Best Practices

### Rate Limiting
- Start with 1-2 requests per second
- Monitor for blocking and adjust accordingly
- Use residential proxies for better success rates

### Proxy Usage
- Use high-quality residential proxies
- Rotate proxies frequently
- Monitor proxy success rates

### Error Handling
- Check logs for patterns in errors
- Adjust retry settings based on error types
- Monitor proxy failure rates

## Troubleshooting

### High Error Rate
1. Reduce request rate (`-r 0.5`)
2. Add more/better proxies
3. Check internet connection
4. Verify proxy credentials

### Proxy Issues
1. Test proxies manually
2. Check proxy format
3. Verify credentials
4. Use different proxy providers

### API Blocking
1. Use residential proxies
2. Reduce request rate significantly
3. Add random delays
4. Rotate user agents

## Legal and Ethical Considerations

⚠️ **Important**: This tool is for educational and legitimate security research purposes only.

### Responsible Usage
- Only check emails you own or have permission to check
- Respect Binance's Terms of Service
- Follow applicable laws and regulations
- Use reasonable rate limits
- Don't abuse the API

### Disclaimer
Users are solely responsible for how they use this tool. The authors are not responsible for any misuse or violations of terms of service.

## Support

For issues or questions:
1. Check the logs in the `logs/` directory
2. Review the troubleshooting section
3. Verify your configuration
4. Test with a small batch first

## Version Information

- **Version**: 1.0
- **Python**: 3.7+
- **Dependencies**: See requirements.txt
- **Platform**: Windows, Linux, macOS
