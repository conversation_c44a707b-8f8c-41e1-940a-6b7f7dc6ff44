#!/usr/bin/env python3
"""
Simple test to verify the Binance Email Checker works
"""

import sys
import os

def test_single_email():
    """Test checking a single email"""
    try:
        # Import our modules
        from email_checker import Binance<PERSON>mail<PERSON>hecker
        from logger import logger
        
        logger.info("Testing single email check...")
        
        # Create checker instance
        checker = BinanceEmailChecker()
        
        # Test with a sample email
        test_email = "<EMAIL>"
        result = checker.check_email(test_email)
        
        logger.info(f"Test result: {result}")
        
        # Verify result structure
        required_keys = ['email', 'status', 'message', 'timestamp', 'duration']
        for key in required_keys:
            if key not in result:
                logger.error(f"Missing key in result: {key}")
                return False
        
        logger.info("✅ Single email test passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Single email test failed: {e}")
        return False

def main():
    """Run simple test"""
    print("Binance Email Checker - Simple Test")
    print("=" * 40)
    
    if test_single_email():
        print("🎉 Test passed! The checker is working.")
    else:
        print("❌ Test failed. Check the error messages above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
