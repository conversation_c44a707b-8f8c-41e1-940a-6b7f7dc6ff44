import threading
import time
import queue
from typing import List, Dict, Callable, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
from config import Config
from logger import logger
from email_checker import BinanceEmail<PERSON>hecker
from utils import Utils

class WorkerManager:
    def __init__(self, proxy_manager=None, max_workers: int = None):
        self.proxy_manager = proxy_manager
        self.max_workers = max_workers or Config.MAX_WORKERS
        self.results = []
        self.results_lock = threading.Lock()
        self.stats = {
            'total': 0,
            'completed': 0,
            'registered': 0,
            'unregistered': 0,
            'errors': 0,
            'start_time': 0,
            'end_time': 0
        }
        self.stats_lock = threading.Lock()
        self.utils = Utils()
    
    def process_emails(self, emails: List[str], progress_callback: Callable = None) -> List[Dict]:
        """Process a list of emails using multiple workers"""
        logger.info(f"Starting email check for {len(emails)} emails with {self.max_workers} workers")
        
        self.stats['total'] = len(emails)
        self.stats['start_time'] = time.time()
        
        # Create progress bar
        with tqdm(total=len(emails), desc="Checking emails", unit="email") as pbar:
            
            # Use ThreadPoolExecutor for better control
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # Submit all tasks
                future_to_email = {
                    executor.submit(self._check_email_with_retry, email): email 
                    for email in emails
                }
                
                # Process completed tasks
                for future in as_completed(future_to_email):
                    email = future_to_email[future]
                    try:
                        result = future.result()
                        self._update_stats(result)
                        
                        with self.results_lock:
                            self.results.append(result)
                        
                        # Update progress bar
                        pbar.set_postfix({
                            'Registered': self.stats['registered'],
                            'Unregistered': self.stats['unregistered'],
                            'Errors': self.stats['errors']
                        })
                        pbar.update(1)
                        
                        # Call progress callback if provided
                        if progress_callback:
                            progress_callback(result, self.stats)
                        
                        # Rate limiting
                        if Config.REQUESTS_PER_SECOND > 0:
                            time.sleep(1.0 / Config.REQUESTS_PER_SECOND)
                            
                    except Exception as e:
                        logger.error(f"Error processing {email}: {e}")
                        error_result = {
                            'email': email,
                            'status': 'error',
                            'message': f'Worker error: {str(e)}',
                            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                            'duration': 0,
                            'proxy_used': None
                        }
                        self._update_stats(error_result)
                        
                        with self.results_lock:
                            self.results.append(error_result)
                        
                        pbar.update(1)
        
        self.stats['end_time'] = time.time()
        self._log_final_stats()
        
        return self.results.copy()
    
    def _check_email_with_retry(self, email: str) -> Dict:
        """Check email with retry logic"""
        checker = BinanceEmailChecker(self.proxy_manager)
        
        for attempt in range(Config.MAX_RETRIES):
            try:
                result = checker.check_email(email)
                
                # If successful or unregistered, return result
                if result['status'] in ['registered', 'unregistered']:
                    return result
                
                # If error and not last attempt, retry
                if attempt < Config.MAX_RETRIES - 1:
                    logger.debug(f"Retry {attempt + 1} for {email}: {result['message']}")
                    time.sleep(Config.RETRY_DELAY * (attempt + 1))  # Exponential backoff
                    continue
                
                # Last attempt failed
                return result
                
            except Exception as e:
                if attempt < Config.MAX_RETRIES - 1:
                    logger.debug(f"Exception on attempt {attempt + 1} for {email}: {e}")
                    time.sleep(Config.RETRY_DELAY * (attempt + 1))
                    continue
                else:
                    return {
                        'email': email,
                        'status': 'error',
                        'message': f'All retries failed: {str(e)}',
                        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                        'duration': 0,
                        'proxy_used': None
                    }
    
    def _update_stats(self, result: Dict):
        """Update statistics thread-safely"""
        with self.stats_lock:
            self.stats['completed'] += 1
            
            if result['status'] == 'registered':
                self.stats['registered'] += 1
            elif result['status'] == 'unregistered':
                self.stats['unregistered'] += 1
            elif result['status'] == 'error':
                self.stats['errors'] += 1
    
    def _log_final_stats(self):
        """Log final statistics"""
        duration = self.stats['end_time'] - self.stats['start_time']
        
        logger.info("=" * 50)
        logger.info("FINAL STATISTICS")
        logger.info("=" * 50)
        logger.info(f"Total emails processed: {self.stats['completed']}/{self.stats['total']}")
        logger.info(f"Registered: {self.stats['registered']}")
        logger.info(f"Unregistered: {self.stats['unregistered']}")
        logger.info(f"Errors: {self.stats['errors']}")
        logger.info(f"Duration: {self.utils.format_duration(duration)}")
        logger.info(f"Rate: {self.stats['completed']/duration:.2f} emails/second")
        
        if self.proxy_manager:
            proxy_stats = self.proxy_manager.get_stats()
            logger.info(f"Proxies used: {proxy_stats['active_proxies']}/{proxy_stats['total_proxies']}")
        
        logger.info("=" * 50)
    
    def get_stats(self) -> Dict:
        """Get current statistics"""
        with self.stats_lock:
            return self.stats.copy()
    
    def get_results(self) -> List[Dict]:
        """Get current results"""
        with self.results_lock:
            return self.results.copy()
