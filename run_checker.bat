@echo off
echo Binance Email Checker
echo =====================

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    pause
    exit /b 1
)

REM Install dependencies if requirements.txt exists
if exist requirements.txt (
    echo Installing dependencies...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo Error: Failed to install dependencies
        pause
        exit /b 1
    )
)

REM Run setup test
echo.
echo Running setup test...
python test_setup.py
if errorlevel 1 (
    echo Setup test failed. Please fix the issues above.
    pause
    exit /b 1
)

REM Run the main checker
echo.
echo Starting Binance Email Checker...
python main.py

pause
