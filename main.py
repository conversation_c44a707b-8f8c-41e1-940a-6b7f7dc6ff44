#!/usr/bin/env python3
"""
Binance Email Checker
A multi-threaded tool to check if emails are registered on Binance
"""

import os
import sys
import argparse
import time
from typing import List, Dict

from config import Config
from logger import logger
from proxy_manager import ProxyManager
from worker_manager import WorkerManager
from utils import Utils

class BinanceChecker:
    def __init__(self):
        self.utils = Utils()
        self.proxy_manager = None
        self.worker_manager = None
        
    def setup_proxy_manager(self, proxy_file: str = None):
        """Initialize proxy manager"""
        proxy_file = proxy_file or Config.PROXIES_FILE
        if os.path.exists(proxy_file):
            self.proxy_manager = ProxyManager(proxy_file)
            logger.info(f"Proxy manager initialized with {len(self.proxy_manager.proxies)} proxies")
        else:
            logger.warning(f"Proxy file {proxy_file} not found. Running without proxies.")
    
    def setup_worker_manager(self, max_workers: int = None):
        """Initialize worker manager"""
        self.worker_manager = WorkerManager(
            proxy_manager=self.proxy_manager,
            max_workers=max_workers or Config.MAX_WORKERS
        )
        logger.info(f"Worker manager initialized with {self.worker_manager.max_workers} workers")
    
    def load_emails(self, email_file: str = None) -> List[str]:
        """Load emails from file"""
        email_file = email_file or Config.EMAILS_FILE
        try:
            emails = self.utils.load_emails(email_file)
            logger.info(f"Loaded {len(emails)} emails from {email_file}")
            return emails
        except Exception as e:
            logger.error(f"Failed to load emails: {e}")
            sys.exit(1)
    
    def run_check(self, emails: List[str]) -> List[Dict]:
        """Run the email checking process"""
        if not emails:
            logger.error("No emails to check")
            return []
        
        logger.info("Starting Binance email checker...")
        logger.info(f"Configuration:")
        logger.info(f"  - Max workers: {Config.MAX_WORKERS}")
        logger.info(f"  - Request timeout: {Config.REQUEST_TIMEOUT}s")
        logger.info(f"  - Max retries: {Config.MAX_RETRIES}")
        logger.info(f"  - Rate limit: {Config.REQUESTS_PER_SECOND} req/s")
        
        # Progress callback function
        def progress_callback(result: Dict, stats: Dict):
            if result['status'] == 'registered':
                logger.result(f"REGISTERED: {result['email']}")
            elif result['status'] == 'unregistered':
                logger.result(f"UNREGISTERED: {result['email']}")
            else:
                logger.debug(f"ERROR: {result['email']} - {result['message']}")
        
        # Run the check
        results = self.worker_manager.process_emails(emails, progress_callback)
        
        return results
    
    def save_results(self, results: List[Dict]):
        """Save results to files"""
        if not results:
            logger.warning("No results to save")
            return
        
        try:
            json_file, csv_file = self.utils.save_results(results, Config.RESULTS_DIR)
            logger.info(f"Results saved to:")
            logger.info(f"  - JSON: {json_file}")
            logger.info(f"  - CSV: {csv_file}")
            
            # Print summary statistics
            stats = self.utils.calculate_stats(results)
            logger.info("\nSUMMARY STATISTICS:")
            logger.info(f"  - Total checked: {stats['total_checked']}")
            logger.info(f"  - Registered: {stats['registered']} ({stats['registered_percentage']:.1f}%)")
            logger.info(f"  - Unregistered: {stats['unregistered']} ({stats['unregistered_percentage']:.1f}%)")
            logger.info(f"  - Errors: {stats['errors']} ({stats['error_percentage']:.1f}%)")
            
        except Exception as e:
            logger.error(f"Failed to save results: {e}")

def create_sample_files():
    """Create sample input files if they don't exist"""
    # Create sample emails file
    if not os.path.exists(Config.EMAILS_FILE):
        sample_emails = [
            "<EMAIL>",
            "<EMAIL>", 
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        with open(Config.EMAILS_FILE, 'w') as f:
            f.write('\n'.join(sample_emails))
        logger.info(f"Created sample emails file: {Config.EMAILS_FILE}")
    
    # Create sample proxies file
    if not os.path.exists(Config.PROXIES_FILE):
        sample_proxies = [
            "# Proxy format examples:",
            "# ip:port",
            "# ip:port:username:password", 
            "# username:password@ip:port",
            "",
            "# Add your proxies below:",
            "# *******:8080",
            "# user:pass@*******:3128"
        ]
        with open(Config.PROXIES_FILE, 'w') as f:
            f.write('\n'.join(sample_proxies))
        logger.info(f"Created sample proxies file: {Config.PROXIES_FILE}")

def main():
    parser = argparse.ArgumentParser(description="Binance Email Checker")
    parser.add_argument('-e', '--emails', help='Email file path', default=Config.EMAILS_FILE)
    parser.add_argument('-p', '--proxies', help='Proxy file path', default=Config.PROXIES_FILE)
    parser.add_argument('-w', '--workers', type=int, help='Number of workers', default=Config.MAX_WORKERS)
    parser.add_argument('-r', '--rate', type=float, help='Requests per second', default=Config.REQUESTS_PER_SECOND)
    parser.add_argument('--create-samples', action='store_true', help='Create sample input files')
    
    args = parser.parse_args()
    
    # Create sample files if requested
    if args.create_samples:
        create_sample_files()
        return
    
    # Update config with command line arguments
    Config.MAX_WORKERS = args.workers
    Config.REQUESTS_PER_SECOND = args.rate
    
    # Initialize checker
    checker = BinanceChecker()
    
    # Setup components
    checker.setup_proxy_manager(args.proxies)
    checker.setup_worker_manager(args.workers)
    
    # Load emails
    emails = checker.load_emails(args.emails)
    
    if not emails:
        logger.error("No valid emails found to check")
        sys.exit(1)
    
    # Run check
    try:
        results = checker.run_check(emails)
        checker.save_results(results)
        
    except KeyboardInterrupt:
        logger.warning("Process interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
