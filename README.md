# Binance Email Checker

A multi-threaded Python tool to check if email addresses are registered on Binance. This tool uses the official Binance API endpoints to determine account registration status.

## Features

- ✅ **Multi-threaded processing** - Check multiple emails simultaneously
- ✅ **Rotating proxy support** - Use multiple proxies to avoid rate limiting
- ✅ **Comprehensive logging** - Detailed logs with colored console output
- ✅ **Rate limiting** - Configurable request rate to avoid detection
- ✅ **Retry logic** - Automatic retry on failures with exponential backoff
- ✅ **Progress tracking** - Real-time progress bar with statistics
- ✅ **Multiple output formats** - Results saved in JSON and CSV
- ✅ **Device fingerprinting** - Realistic browser headers and device info
- ✅ **Error handling** - Robust error handling and recovery

## Installation

1. **Clone or download** this repository
2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

## Configuration

### Environment Variables
Copy `.env.example` to `.env` and modify settings:

```env
MAX_WORKERS=10              # Number of concurrent threads
REQUEST_TIMEOUT=30          # Request timeout in seconds
MAX_RETRIES=3              # Number of retries on failure
RETRY_DELAY=1.0            # Base delay between retries
REQUESTS_PER_SECOND=2.0    # Rate limiting
```

### Input Files

#### emails.txt
Add one email per line:
```
<EMAIL>
<EMAIL>
<EMAIL>
```

#### proxies.txt
Add proxies in supported formats:
```
# Format 1: ip:port
*******:8080

# Format 2: ip:port:username:password
*******:3128:user:pass

# Format 3: username:password@ip:port
user:pass@**********:8080
```

## Usage

### Basic Usage
```bash
python main.py
```

### Advanced Usage
```bash
# Custom files and settings
python main.py -e my_emails.txt -p my_proxies.txt -w 20 -r 1.5

# Create sample files
python main.py --create-samples
```

### Command Line Options
- `-e, --emails`: Email file path (default: emails.txt)
- `-p, --proxies`: Proxy file path (default: proxies.txt)
- `-w, --workers`: Number of workers (default: 10)
- `-r, --rate`: Requests per second (default: 2.0)
- `--create-samples`: Create sample input files

## Output

### Console Output
Real-time progress with colored logging:
```
14:30:15 - INFO - Starting email check for 100 emails with 10 workers
14:30:16 - RESULT: REGISTERED: <EMAIL>
14:30:17 - RESULT: UNREGISTERED: <EMAIL>
```

### File Output
Results are saved in the `results/` directory:
- `results_YYYYMMDD_HHMMSS.json` - Detailed JSON results
- `results_YYYYMMDD_HHMMSS.csv` - CSV format for spreadsheets

### Log Files
Detailed logs are saved in the `logs/` directory:
- `checker_YYYYMMDD_HHMMSS.log` - Full debug logs
- `results_YYYYMMDD_HHMMSS.log` - Results only

## Result Format

Each email check returns:
```json
{
  "email": "<EMAIL>",
  "status": "registered|unregistered|error",
  "message": "Description of result",
  "timestamp": "2024-01-15 14:30:15",
  "duration": 1.23,
  "proxy_used": "*******:8080"
}
```

## How It Works

The tool follows Binance's authentication flow:

1. **Precheck Request** - Initial email validation
2. **Response Analysis** - Determines registration status based on API response
3. **Error Handling** - Distinguishes between unregistered emails and API errors

## Rate Limiting & Best Practices

- **Default rate**: 2 requests/second (configurable)
- **Proxy rotation**: Automatically rotates through available proxies
- **Retry logic**: Exponential backoff on failures
- **Random delays**: Adds randomization to avoid detection patterns

## Troubleshooting

### Common Issues

1. **No proxies working**
   - Check proxy format and credentials
   - Test proxies manually
   - Reduce concurrent workers

2. **High error rate**
   - Reduce request rate (`-r` parameter)
   - Add more proxies
   - Check internet connection

3. **Blocked requests**
   - Use residential proxies
   - Reduce rate to 0.5-1 req/s
   - Add delays between batches

### Debug Mode
Enable debug logging by modifying `logger.py`:
```python
console_handler.setLevel(logging.DEBUG)
```

## Legal Notice

This tool is for educational and legitimate security research purposes only. Users are responsible for:
- Complying with Binance's Terms of Service
- Respecting rate limits and API usage policies
- Using only emails they own or have permission to check
- Following applicable laws and regulations

## License

This project is provided as-is for educational purposes. Use responsibly and at your own risk.
