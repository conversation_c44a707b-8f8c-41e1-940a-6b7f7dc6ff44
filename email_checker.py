import requests
import json
import time
import random
from typing import Dict, Optional, <PERSON><PERSON>
from config import Config
from logger import logger
from utils import Utils

class BinanceEmailChecker:
    def __init__(self, proxy_manager=None):
        self.proxy_manager = proxy_manager
        self.utils = Utils()
        self.session = requests.Session()
        
        # Set default timeout
        self.session.timeout = Config.REQUEST_TIMEOUT
    
    def check_email(self, email: str) -> Dict[str, any]:
        """
        Check if email is registered on Binance
        Returns: {'email': str, 'status': str, 'message': str, 'timestamp': str}
        """
        start_time = time.time()
        result = {
            'email': email,
            'status': 'unknown',
            'message': '',
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'duration': 0,
            'proxy_used': None
        }
        
        try:
            # Validate email format
            if not self.utils.validate_email(email):
                result['status'] = 'error'
                result['message'] = 'Invalid email format'
                return result
            
            # Get proxy if available
            proxy = None
            if self.proxy_manager:
                proxy = self.proxy_manager.get_proxy()
                if proxy:
                    result['proxy_used'] = proxy['raw']
            
            # Step 1: Precheck request
            precheck_result = self._precheck_request(email, proxy)
            if not precheck_result['success']:
                result['status'] = 'error'
                result['message'] = precheck_result['message']
                if proxy:
                    self.proxy_manager.mark_proxy_failed(proxy)
                return result
            
            # Extract session ID and validation ID from precheck
            session_id = precheck_result.get('session_id')
            validate_id = precheck_result.get('validate_id')
            
            if not session_id or not validate_id:
                # If we get here, the email might be unregistered
                result['status'] = 'unregistered'
                result['message'] = 'Email not found in Binance database'
                return result
            
            # If we got session_id and validate_id, email is likely registered
            result['status'] = 'registered'
            result['message'] = 'Email found in Binance database'
            
        except requests.exceptions.RequestException as e:
            result['status'] = 'error'
            result['message'] = f'Network error: {str(e)}'
            if proxy:
                self.proxy_manager.mark_proxy_failed(proxy)
            logger.debug(f"Network error for {email}: {e}")
            
        except Exception as e:
            result['status'] = 'error'
            result['message'] = f'Unexpected error: {str(e)}'
            logger.debug(f"Unexpected error for {email}: {e}")
        
        finally:
            result['duration'] = round(time.time() - start_time, 2)
        
        return result
    
    def _precheck_request(self, email: str, proxy: Optional[Dict] = None) -> Dict:
        """Perform the initial precheck request"""
        headers = self.utils.generate_headers(email)
        
        payload = {
            "email": email,
            "bizType": "login"
        }
        
        proxies = proxy if proxy else None
        
        try:
            response = self.session.post(
                Config.PRECHECK_URL,
                headers=headers,
                json=payload,
                proxies=proxies,
                timeout=Config.REQUEST_TIMEOUT
            )
            
            logger.debug(f"Precheck response for {email}: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    
                    # Check if response indicates success
                    if data.get('success', False):
                        return {
                            'success': True,
                            'session_id': data.get('data', {}).get('sessionId'),
                            'validate_id': data.get('data', {}).get('securityCheckResponseValidateId'),
                            'data': data
                        }
                    else:
                        # Check error codes to determine if email exists
                        error_code = data.get('code')
                        error_msg = data.get('message', '')
                        
                        # Common error codes that indicate unregistered email
                        unregistered_codes = ['100001003', '100001004', '100001005']
                        
                        if str(error_code) in unregistered_codes or 'not found' in error_msg.lower():
                            return {
                                'success': False,
                                'message': 'Email not registered',
                                'unregistered': True
                            }
                        else:
                            return {
                                'success': False,
                                'message': f'API error: {error_msg} (Code: {error_code})',
                                'unregistered': False
                            }
                            
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'message': 'Invalid JSON response',
                        'unregistered': False
                    }
            else:
                return {
                    'success': False,
                    'message': f'HTTP {response.status_code}: {response.text[:100]}',
                    'unregistered': False
                }
                
        except requests.exceptions.Timeout:
            return {
                'success': False,
                'message': 'Request timeout',
                'unregistered': False
            }
        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'message': 'Connection error',
                'unregistered': False
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'Request failed: {str(e)}',
                'unregistered': False
            }
    
    def _add_random_delay(self):
        """Add random delay to avoid detection"""
        delay = random.uniform(0.5, 2.0)
        time.sleep(delay)
