#!/bin/bash

echo "Binance Email Checker"
echo "====================="

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed"
    echo "Please install Python 3.7+ from https://python.org"
    exit 1
fi

# Install dependencies if requirements.txt exists
if [ -f "requirements.txt" ]; then
    echo "Installing dependencies..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "Error: Failed to install dependencies"
        exit 1
    fi
fi

# Run setup test
echo ""
echo "Running setup test..."
python3 test_setup.py
if [ $? -ne 0 ]; then
    echo "Setup test failed. Please fix the issues above."
    exit 1
fi

# Run the main checker
echo ""
echo "Starting Binance Email Checker..."
python3 main.py

echo "Press any key to continue..."
read -n 1
