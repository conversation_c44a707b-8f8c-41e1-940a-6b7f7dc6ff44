#!/usr/bin/env python3
"""
Demo script showing how to use the Binance Email Checker
"""

import time
from email_checker import BinanceEmailChecker
from logger import logger

def demo_single_check():
    """Demo checking a single email"""
    logger.info("=== Demo: Single Email Check ===")
    
    checker = BinanceEmailChecker()
    
    # Test emails (these are just examples)
    test_emails = [
        "<EMAIL>",
        "<EMAIL>", 
        "<EMAIL>"
    ]
    
    for email in test_emails:
        logger.info(f"Checking: {email}")
        result = checker.check_email(email)
        
        status = result['status']
        message = result['message']
        duration = result['duration']
        
        if status == 'registered':
            logger.info(f"✅ REGISTERED: {email} ({duration}s)")
        elif status == 'unregistered':
            logger.info(f"❌ UNREGISTERED: {email} ({duration}s)")
        else:
            logger.warning(f"⚠️  ERROR: {email} - {message} ({duration}s)")
        
        # Small delay between requests
        time.sleep(1)

def demo_with_proxy():
    """Demo checking with proxy"""
    logger.info("=== Demo: Check with Proxy ===")
    
    from proxy_manager import ProxyManager
    
    # Try to load proxies
    try:
        proxy_manager = ProxyManager("proxies.txt")
        if proxy_manager.proxies:
            logger.info(f"Loaded {len(proxy_manager.proxies)} proxies")
            
            checker = BinanceEmailChecker(proxy_manager)
            result = checker.check_email("<EMAIL>")
            
            logger.info(f"Result with proxy: {result}")
        else:
            logger.warning("No proxies available for demo")
            
    except Exception as e:
        logger.warning(f"Proxy demo failed: {e}")

def demo_batch_check():
    """Demo checking multiple emails"""
    logger.info("=== Demo: Batch Email Check ===")
    
    from worker_manager import WorkerManager
    
    # Sample emails
    emails = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ]
    
    # Create worker manager with 2 workers
    worker_manager = WorkerManager(max_workers=2)
    
    logger.info(f"Checking {len(emails)} emails with 2 workers...")
    
    # Process emails
    results = worker_manager.process_emails(emails)
    
    # Show results
    for result in results:
        email = result['email']
        status = result['status']
        logger.info(f"{email}: {status}")

def main():
    """Run all demos"""
    logger.info("Binance Email Checker - Demo")
    logger.info("=" * 50)
    
    try:
        # Demo 1: Single email check
        demo_single_check()
        
        print("\n" + "=" * 50)
        
        # Demo 2: Check with proxy
        demo_with_proxy()
        
        print("\n" + "=" * 50)
        
        # Demo 3: Batch check
        demo_batch_check()
        
        logger.info("Demo completed!")
        
    except KeyboardInterrupt:
        logger.warning("Demo interrupted by user")
    except Exception as e:
        logger.error(f"Demo failed: {e}")

if __name__ == "__main__":
    main()
